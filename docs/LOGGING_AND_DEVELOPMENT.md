# Логирование и разработка

## Конфигурация логирования

### Настройка уровня логирования через .env файл

Уровень логирования теперь настраивается через переменную окружения `LOG_LEVEL` в файле `.env`:

```bash
# .env
LOG_LEVEL=DEBUG  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### Доступные уровни логирования

- **DEBUG** - Детальная отладочная информация
- **INFO** - Общая информация о работе приложения (по умолчанию)
- **WARNING** - Предупреждения о потенциальных проблемах
- **ERROR** - Ошибки, которые не останавливают работу приложения
- **CRITICAL** - Критические ошибки, которые могут остановить приложение

### Структурированное логирование

Приложение поддерживает структурированное логирование с помощью `structlog`:

```bash
# .env
STRUCTURED_LOGGING=true  # Использовать structlog
LOG_JSON_FORMAT=false    # JSON формат для production
LOG_REQUEST_BODY=false   # Логировать тела запросов
LOG_RESPONSE_BODY=false  # Логировать тела ответов
```

## Режим разработки

### Auto-reload через .env файл

Для автоматической перезагрузки при изменении кода установите в `.env`:

```bash
# .env
RELOAD=true
```

### Запуск с параметрами командной строки

Вы можете переопределить настройки из `.env` файла через командную строку:

```bash
# Включить auto-reload
python main.py --reload

# Установить уровень логирования
python main.py --log-level DEBUG

# Использовать JSON формат логов
python main.py --json-logs

# Комбинирование параметров
python main.py --reload --log-level DEBUG --json-logs
```

## Примеры конфигурации

### Разработка

```bash
# .env для разработки
LOG_LEVEL=DEBUG
RELOAD=true
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false
LOG_REQUEST_BODY=true
LOG_RESPONSE_BODY=false
```

### Production

```bash
# .env для production
LOG_LEVEL=INFO
RELOAD=false
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=true
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false
```

### Отладка

```bash
# .env для отладки
LOG_LEVEL=DEBUG
RELOAD=true
STRUCTURED_LOGGING=true
LOG_JSON_FORMAT=false
LOG_REQUEST_BODY=true
LOG_RESPONSE_BODY=true
```

## Логи в файлах

Логи автоматически сохраняются в файл `app.log` с ротацией:

- **Размер файла**: максимум 10 MB
- **Хранение**: 1 неделя
- **Формат**: человекочитаемый (не JSON)

## Миграция со старой системы

### Было (deprecated):

```bash
python main.py --debug
```

### Стало:

```bash
# Через .env файл
LOG_LEVEL=DEBUG

# Или через командную строку
python main.py --log-level DEBUG
```

## Полезные команды

```bash
# Запуск в режиме разработки
python main.py --reload --log-level DEBUG

# Запуск с JSON логами для тестирования
python main.py --json-logs --log-level INFO

# Запуск в production режиме (используются настройки из .env)
python main.py
```
