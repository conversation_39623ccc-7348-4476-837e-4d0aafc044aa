[project]
name = "yt-subs-api4"
version = "0.1.0"
description = "API for downloading YouTube video subtitles and summarizing text using Gemini AI"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.18",
    "fastapi>=0.115.12",
    "loguru>=0.7.3",
    "structlog>=25.3.0",
    "slowapi>=0.1.9",
    "pysocks>=1.7.1",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
    "websockets>=15.0.1",
    "yt-dlp>=2025.5.22",
    "google-generativeai>=0.8.4",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.0",
    "rich>=14.0.0",
    "mdformat>=0.7.22",
    "mdformat-gfm>=0.4.1",
    "google>=3.0.0",
    "google-genai>=1.15.0",
    "psycopg2-binary>=2.9.10",
    "psutil>=6.1.0",
    "pydantic-settings>=2.9.1",
]
