import sys
import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
from loguru import logger as loguru_logger


def setup_structured_logging(log_level: str = "INFO", use_json: bool = False) -> structlog.BoundLogger:
    """
    Setup structured logging with both loguru and structlog.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRIT<PERSON>AL)
        use_json: Use JSON format for structured logs

    Returns:
        Configured structlog logger
    """
    # Ensure log level is uppercase
    log_level = log_level.upper()
    debug = log_level == "DEBUG"

    # Configure structlog processors
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
    ]

    if debug:
        # Add more detailed processors for debug mode
        processors.extend([
            structlog.processors.CallsiteParameterAdder(
                parameters=[structlog.processors.CallsiteParameter.FILENAME,
                           structlog.processors.CallsiteParameter.FUNC_NAME,
                           structlog.processors.CallsiteParameter.LINENO]
            ),
        ])

    if use_json:
        # JSON format for production/structured logging
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Human-readable format for development
        processors.append(structlog.dev.ConsoleRenderer(colors=True))

    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level)
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Setup loguru for backward compatibility
    setup_loguru_logging(log_level=log_level)

    return structlog.get_logger()


def setup_loguru_logging(log_level: str = "INFO"):
    """
    Setup loguru logging for backward compatibility.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # Remove default handler
    loguru_logger.remove()

    # Ensure log level is uppercase
    log_level = log_level.upper()
    debug = log_level == "DEBUG"

    # Add file handler with structured format
    log_file = Path(__file__).parent.parent / "app.log"
    loguru_logger.add(
        log_file,
        rotation="10 MB",
        retention="1 week",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        backtrace=True,
        diagnose=True,
        serialize=False,  # Keep human-readable format for file logs
    )

    # Add stdout handler with more detailed format for debug mode
    if debug:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    else:
        format_string = (
            "<green>{time:HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<level>{message}</level>"
        )

    loguru_logger.add(
        sys.stdout,
        colorize=True,
        level=log_level,
        format=format_string
    )


def setup_logging(log_level: str = "INFO", structured: bool = True, use_json: bool = False) -> Any:
    """
    Setup logging system.

    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        structured: Use structured logging (structlog) if True, otherwise use loguru
        use_json: Use JSON format for structured logs

    Returns:
        Configured logger (structlog or loguru)
    """
    if structured:
        return setup_structured_logging(log_level=log_level, use_json=use_json)
    else:
        setup_loguru_logging(log_level=log_level)
        return loguru_logger


def get_logger(name: Optional[str] = None) -> structlog.BoundLogger:
    """
    Get a structured logger instance.

    Args:
        name: Logger name (optional)

    Returns:
        Structured logger instance
    """
    if name:
        return structlog.get_logger(name)
    return structlog.get_logger()
