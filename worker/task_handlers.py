import asyncio
import time
import os  # Import os module
# import hashlib
from typing import Dict, Optional, Set, Any
from loguru import logger

from models.schemas import (
    # SubtitleResponse,
    # SummarizeResponse,
    TaskStatus,
    # WebSocketMessage,
    WebSocketSubtitleMessage,
    WebSocketSummarizeMessage,
    MessageType,
    # VideoListResponse,
)
from worker.subtitles.youtube import YouTubeSubtitleDownloader
from worker.summarizers.summarizer_new import TextSummarizer


class TaskHandler:
    """Класс для обработки различных типов задач"""

    def __init__(self, timeout_seconds: int, ws_notifier: Any):
        self.downloader: Optional[YouTubeSubtitleDownloader] = None
        self.summarizer: Optional[TextSummarizer] = None
        self.timeout_seconds = timeout_seconds
        self.ws_notifier = ws_notifier
        self.processing_tasks: Set[str] = set()
        self.task_timeouts: Dict[str, float] = {}

    async def initialize(self, proxy_url: Optional[str] = None):
        """Инициализация обработчиков задач"""
        cookies_file = os.getenv("COOKIES_FILE")  # Read COOKIES_FILE env var
        self.downloader = YouTubeSubtitleDownloader(
            proxy_url=proxy_url, cookies_file=cookies_file)  # Pass cookies_file
        self.summarizer = TextSummarizer()
        logger.debug("TaskHandler initialized.")

    async def process_video_list_task(self, task_id: str, data: tuple, tasks: Dict[str, Any]):
        """Process task to extract video IDs from URL"""
        url, _ = data  # client_uid is ignored in internal processing
        logger.debug(
            f"Processing video_list task {task_id} for URL: {url}"
        )
        try:
            task = tasks[task_id]
            task.status = TaskStatus.PROCESSING
            logger.debug(
                f"Video_list task {task_id} status set to PROCESSING.")

            video_ids = await asyncio.get_running_loop().run_in_executor(
                None, self.downloader.extract_video_ids, url
            )
            logger.debug(
                f"Extracted {len(video_ids)} video IDs for task {task_id}.")

            task.video_ids = video_ids
            task.status = TaskStatus.COMPLETED
            logger.debug(f"Video_list task {task_id} status set to COMPLETED.")
        except Exception as e:
            logger.error(
                f"Error processing video_list task {task_id}: {str(e)}", exc_info=True
            )
            if task_id in tasks:
                tasks[task_id].status = TaskStatus.FAILED
                tasks[task_id].error = str(e)
        finally:
            self.processing_tasks.discard(task_id)
            logger.debug(
                f"Finished processing video_list task {task_id}. Discarded from processing_tasks."
            )

    async def process_subtitle_task(self, video_id: str, data: tuple, tasks: Dict[str, Any]):
        """Process a subtitle task"""
        url, _ = data  # client_uid is ignored in internal processing
        logger.debug(
            f"Processing subtitle task {video_id} for URL: {url}"
        )
        # Mark task as being processed
        self.processing_tasks.add(video_id)
        self.task_timeouts[video_id] = time.time()
        logger.debug(
            f"Subtitle task {video_id} marked as processing, timeout set.")
        try:
            task = tasks[video_id]
            task.status = TaskStatus.PROCESSING
            logger.debug(
                f"Notifying subscribers: subtitle task {video_id} is PROCESSING."
            )
            await self.ws_notifier.notify_subscribers(
                video_id,
                WebSocketSubtitleMessage(
                    type=MessageType.STATUS,
                    task_id=video_id,
                    status=TaskStatus.PROCESSING,
                    client_uid=getattr(task, 'client_uid', None),  # Use client_uid from task object
                ),
            )

            if not self.downloader:
                logger.error(
                    "Downloader not initialized in process_subtitle_task.")
                raise RuntimeError("Downloader not initialized")

            logger.debug(
                f"Starting subtitle download for {video_id} with timeout {self.timeout_seconds}s."
            )

            async def download_with_timeout():
                return await asyncio.wait_for(
                    self.downloader.download_subtitles(url),
                    timeout=self.timeout_seconds,
                )

            download_task_obj = asyncio.create_task(download_with_timeout())
            try:
                result = await download_task_obj
                title, original_language, publish_date, en_subs, ru_subs = result
                logger.debug(
                    f"Subtitle download complete for {video_id}. Title: {title}, Lang: {original_language}"
                )

                if not en_subs and not ru_subs:
                    logger.warning(f"No subtitles found for {video_id}.")
                    raise RuntimeError("No subtitles found")

                task.title = title
                task.original_language = original_language
                task.publish_date = publish_date
                task.en_subtitles = en_subs
                task.ru_subtitles = ru_subs
                task.status = TaskStatus.COMPLETED
                logger.debug(
                    f"Subtitle task {video_id} status set to COMPLETED.")
            except asyncio.TimeoutError:
                download_task_obj.cancel()
                logger.warning(
                    f"Subtitle download timed out for task {video_id}.")
                try:
                    await download_task_obj
                except asyncio.CancelledError:
                    logger.debug(
                        f"Download task for {video_id} acknowledged cancellation post-timeout."
                    )
                raise RuntimeError(
                    f"Task timed out after {self.timeout_seconds} seconds"
                )
            except asyncio.CancelledError:
                logger.info(
                    f"Subtitle download task {video_id} was externally cancelled."
                )
                raise

            logger.debug(
                f"Notifying subscribers: subtitle task {video_id} is COMPLETED."
            )
            await self.ws_notifier.notify_subscribers(
                video_id,
                WebSocketSubtitleMessage(
                    type=MessageType.RESULT,
                    task_id=video_id,
                    status=TaskStatus.COMPLETED,
                    title=title,
                    original_language=original_language,
                    publish_date=publish_date,
                    en_subtitles=en_subs,
                    ru_subtitles=ru_subs,
                    client_uid=client_uid,
                ),
            )
        except asyncio.CancelledError:
            logger.info(f"Process subtitle task {video_id} was cancelled.")
            raise
        except Exception as e:
            logger.error(
                f"Error processing subtitle task {video_id}: {str(e)}", exc_info=True
            )
            if video_id in tasks:
                tasks[video_id].status = TaskStatus.FAILED
                tasks[video_id].error = str(e)
                logger.debug(
                    f"Notifying subscribers: subtitle task {video_id} FAILED.")
                await self.ws_notifier.notify_subscribers(
                    video_id,
                    WebSocketSubtitleMessage(
                        type=MessageType.STATUS,
                        task_id=video_id,
                        status=TaskStatus.FAILED,
                        error=str(e),
                        client_uid=client_uid,
                    ),
                )
        finally:
            self.processing_tasks.discard(video_id)
            logger.debug(
                f"Finished processing subtitle task {video_id}. Discarded from processing_tasks."
            )

    async def process_summarize_task(self, task_id: str, data: tuple, tasks: Dict[str, Any]):
        """Process a summarization task"""
        text, mode, _ = data  # client_uid is ignored in internal processing
        logger.debug(
            f"Processing summarize task {task_id}, mode: {mode}, text length: {len(text)}"
        )
        self.processing_tasks.add(task_id)
        self.task_timeouts[task_id] = time.time()
        logger.debug(
            f"Summarize task {task_id} marked as processing, timeout set.")
        try:
            task = tasks[task_id]
            task.status = TaskStatus.PROCESSING
            logger.debug(
                f"Notifying subscribers: summarize task {task_id} is PROCESSING."
            )
            await self.ws_notifier.notify_subscribers(
                task_id,
                WebSocketSummarizeMessage(
                    type=MessageType.STATUS,
                    task_id=task_id,
                    status=TaskStatus.PROCESSING,
                    client_uid=getattr(task, 'client_uid', None),  # Use client_uid from task object
                ),
            )

            if not self.summarizer:
                logger.error(
                    "Summarizer not initialized in process_summarize_task.")
                raise RuntimeError("Summarizer not initialized")

            logger.debug(
                f"Starting summarization for {task_id} with timeout {self.timeout_seconds}s."
            )

            async def summarize_with_timeout():
                return await asyncio.wait_for(
                    self.summarizer.summarize(text, mode=mode),
                    timeout=self.timeout_seconds,
                )

            summarize_task_obj = asyncio.create_task(summarize_with_timeout())
            try:
                summary = await summarize_task_obj
                logger.debug(
                    f"Summarization complete for {task_id}. Summary length: {len(summary) if summary else 0}"
                )
                task.summary = summary
                task.status = TaskStatus.COMPLETED
                logger.debug(
                    f"Summarize task {task_id} status set to COMPLETED.")
            except asyncio.TimeoutError:
                summarize_task_obj.cancel()
                logger.warning(f"Summarization timed out for task {task_id}.")
                try:
                    await summarize_task_obj
                except asyncio.CancelledError:
                    logger.debug(
                        f"Summarize task for {task_id} acknowledged cancellation post-timeout."
                    )
                raise RuntimeError(
                    f"Task timed out after {self.timeout_seconds} seconds"
                )
            except asyncio.CancelledError:
                logger.info(
                    f"Summarize task {task_id} was externally cancelled.")
                raise

            logger.debug(
                f"Notifying subscribers: summarize task {task_id} is COMPLETED."
            )
            await self.ws_notifier.notify_subscribers(
                task_id,
                WebSocketSummarizeMessage(
                    type=MessageType.RESULT,
                    task_id=task_id,
                    status=TaskStatus.COMPLETED,
                    summary=summary,
                    client_uid=client_uid,
                ),
            )
        except asyncio.CancelledError:
            logger.info(f"Process summarize task {task_id} was cancelled.")
            raise
        except Exception as e:
            logger.error(
                f"Error processing summarization task {task_id}: {str(e)}",
                exc_info=True,
            )
            if task_id in tasks:
                tasks[task_id].status = TaskStatus.FAILED
                tasks[task_id].error = str(e)
                logger.debug(
                    f"Notifying subscribers: summarize task {task_id} FAILED.")
                # Отправляем сообщение об ошибке подписчикам
                error_message_ws = WebSocketSummarizeMessage(
                    type=MessageType.STATUS,
                    task_id=task_id,
                    status=TaskStatus.FAILED,
                    error=str(e),
                    client_uid=client_uid,
                )
                await self.ws_notifier.notify_subscribers(task_id, error_message_ws)

                # Закрываем WebSocket соединения для этой задачи
                await self.ws_notifier.close_connections_for_task(task_id, str(e))
        finally:
            self.processing_tasks.discard(task_id)
            logger.debug(
                f"Finished processing summarize task {task_id}. Discarded from processing_tasks."
            )
